#!/usr/bin/env python3
"""
Test script to verify the new "How to Earn Genesis Token❓" button functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from telegram import KeyboardButton, ReplyKeyboardMarkup
from final_bot import GenesisBotApp

def test_main_keyboard():
    """Test the main keyboard layout"""
    print("🧪 Testing Main Keyboard Layout...")

    bot = GenesisBotApp()
    keyboard = bot.get_main_keyboard()
    
    print("\n📱 Main Menu Keyboard Structure:")
    print("=" * 50)
    
    for i, row in enumerate(keyboard.keyboard):
        print(f"Row {i+1}:")
        for j, button in enumerate(row):
            print(f"  Button {j+1}: '{button.text}'")
    
    print("\n✅ Expected Layout:")
    print("Row 1: '🚀 Earn Genesis Token', '💰 Genesis Token Balance'")
    print("Row 2: 'How to Earn Genesis Token❓'")
    print("Row 3: '🏆 Top 5 Users', 'ℹ️ About Genesis Bot'")
    
    # Verify the layout
    expected_layout = [
        ["🚀 Earn Genesis Token", "💰 Genesis Token Balance"],
        ["How to Earn Genesis Token❓"],
        ["🏆 Top 5 Users", "ℹ️ About Genesis Bot"]
    ]
    
    actual_layout = [[button.text for button in row] for row in keyboard.keyboard]
    
    if actual_layout == expected_layout:
        print("\n✅ Keyboard layout matches expected structure!")
        return True
    else:
        print("\n❌ Keyboard layout does not match expected structure!")
        print(f"Expected: {expected_layout}")
        print(f"Actual: {actual_layout}")
        return False

def test_how_to_earn_message():
    """Test the how to earn message content"""
    print("\n🧪 Testing How to Earn Message Content...")
    
    expected_message = """
🚀 **HOW TO EARN GENESIS TOKENS:**
💰 *Share your referral link with friends*
🎁 *Earn* **50 Genesis Tokens** *per referral*
🎉 *Your friends get* **25 Genesis Tokens** *for joining!*

🏆 *The* **Top 5 users** *on the leaderboard each week win guaranteed prizes*
🎉 *Plus, a* **weekly giveaway** *for all active users – everyone gets a shot*

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

✨ **Start earning now by inviting your friends!** 🚀
            """
    
    print("\n📝 How to Earn Message:")
    print("=" * 50)
    print(expected_message.strip())
    
    # Check for key elements
    key_elements = [
        "🚀 **HOW TO EARN GENESIS TOKENS:**",
        "**50 Genesis Tokens**",
        "**25 Genesis Tokens**",
        "**Top 5 users**",
        "**weekly giveaway**",
        "✨ **Start earning now by inviting your friends!** 🚀"
    ]
    
    print("\n✅ Key Elements Check:")
    all_present = True
    for element in key_elements:
        if element in expected_message:
            print(f"  ✅ Found: {element}")
        else:
            print(f"  ❌ Missing: {element}")
            all_present = False
    
    return all_present

def main():
    """Run all tests"""
    print("🚀 Genesis Bot - New Button Test Suite")
    print("=" * 60)
    
    keyboard_test = test_main_keyboard()
    message_test = test_how_to_earn_message()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Keyboard Layout: {'✅ PASS' if keyboard_test else '❌ FAIL'}")
    print(f"  Message Content: {'✅ PASS' if message_test else '❌ FAIL'}")
    
    if keyboard_test and message_test:
        print("\n🎉 All tests passed! The new button is ready to use.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
